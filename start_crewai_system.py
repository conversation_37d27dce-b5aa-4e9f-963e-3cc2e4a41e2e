#!/usr/bin/env python3
"""
CrewAI System Startup Script
Automated startup with CrewAI agent initialization
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

class CrewAISystemStarter:
    def __init__(self):
        self.api_port = int(os.getenv("API_PORT", "8000"))
        self.streamlit_port = 8501
    
    def check_crewai_dependencies(self) -> bool:
        """Check CrewAI-specific dependencies"""
        print("🤖 Checking CrewAI dependencies...")
        print(f"🔍 Python executable: {sys.executable}")
        print(f"🔍 Python version: {sys.version}")
        print(f"🔍 Python path: {sys.path}")
        print(f"🔍 Current working directory: {os.getcwd()}")

        required_packages = [
            "crewai", "crewai-tools", "langchain", "langchain-community"
        ]

        missing_packages = []

        for package in required_packages:
            print(f"🔍 Attempting to import: {package}")
            try:
                __import__(package.replace("-", "_"))
                print(f"✅ Successfully imported: {package}")
            except ImportError as e:
                print(f"❌ Failed to import {package}: {e}")
                missing_packages.append(package)

        if missing_packages:
            print(f"❌ Missing CrewAI packages: {', '.join(missing_packages)}")
            print("Run: pip install -r requirements.txt")
            return False

        print("✅ CrewAI dependencies satisfied")
        return True
    
    def start_crewai_api_server(self) -> bool:
        """Start the CrewAI-enhanced API server"""
        print("🤖 Starting CrewAI-enhanced API server...")
        
        try:
            cmd = [sys.executable, "main_enhanced_crewai.py"]
            
            self.api_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait for CrewAI initialization
            max_attempts = 60  # CrewAI needs more time to initialize
            for attempt in range(max_attempts):
                try:
                    response = requests.get(f"http://localhost:{self.api_port}/", timeout=5)
                    if response.status_code == 200:
                        data = response.json()
                        if "crewai" in data.get("mode", "").lower():
                            print(f"✅ CrewAI API server started on port {self.api_port}")
                            print(f"🤖 Available crews: {len(data.get('available_crews', []))}")
                            print(f"🤖 Available agents: {len(data.get('available_agents', []))}")
                            return True
                except requests.exceptions.RequestException:
                    pass
                
                if attempt < max_attempts - 1:
                    print(f"⏳ Waiting for CrewAI initialization... ({attempt + 1}/{max_attempts})")
                    time.sleep(3)  # Longer wait for CrewAI
            
            print("❌ CrewAI API server failed to start")
            return False
            
        except Exception as e:
            print(f"❌ Error starting CrewAI API server: {e}")
            return False
    
    def start_crewai_streamlit(self) -> bool:
        """Start the CrewAI-enhanced Streamlit frontend"""
        print("🎨 Starting CrewAI-enhanced Streamlit frontend...")
        
        try:
            cmd = [
                sys.executable, "-m", "streamlit", "run", "app_enhanced_crewai.py",
                "--server.port", str(self.streamlit_port),
                "--server.address", "0.0.0.0",
                "--server.headless", "true"
            ]
            
            self.streamlit_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            time.sleep(8)  # More time for CrewAI Streamlit to start
            
            try:
                response = requests.get(f"http://localhost:{self.streamlit_port}", timeout=10)
                if response.status_code == 200:
                    print(f"✅ CrewAI Streamlit started on port {self.streamlit_port}")
                    return True
            except Exception:
                pass
            
            print(f"⚠️ CrewAI Streamlit may still be starting on port {self.streamlit_port}")
            return True
            
        except Exception as e:
            print(f"❌ Error starting CrewAI Streamlit: {e}")
            return False
    
    def start_crewai_system(self):
        """Start the complete CrewAI system"""
        print("🤖 Starting CrewAI-Enhanced AWS Cost Optimization System")
        print("=" * 80)
        
        # Check CrewAI dependencies
        if not self.check_crewai_dependencies():
            return False
        
        # Start CrewAI services
        if not self.start_crewai_api_server():
            print("❌ Failed to start CrewAI API server")
            return False
        
        if not self.start_crewai_streamlit():
            print("❌ Failed to start CrewAI Streamlit")
            return False
        
        print("\n" + "=" * 80)
        print("✅ CrewAI System startup complete!")
        print(f"🌐 CrewAI Web Interface: http://localhost:{self.streamlit_port}")
        print(f"🔧 CrewAI API Documentation: http://localhost:{self.api_port}/docs")
        print(f"🤖 Multi-Agent Crews: /crews endpoint")
        print(f"🤖 AI Agents Info: /agents endpoint")
        print("=" * 80)
        
        return True

def main():
    """Main entry point"""
    starter = CrewAISystemStarter()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "start":
            starter.start_crewai_system()
        else:
            print("Usage: python start_crewai_system.py [start]")
            print("  start  - Start CrewAI system")
    else:
        # Default action is to start
        starter.start_crewai_system()

if __name__ == "__main__":
    main()
